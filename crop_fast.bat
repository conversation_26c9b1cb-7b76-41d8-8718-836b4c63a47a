@echo off
setlocal enabledelayedexpansion

if "%~1"=="" (
    echo Drag and drop a video file onto this script, Puppy.
    pause
    exit /b
)

set "INPUT=%~1"
set "FILENAME=%~n1"
set "EXT=%~x1"
set "OUTPUT=%FILENAME%_cropped%EXT%"

REM === Temp file for ffprobe output ===
set "TMPFILE=%TEMP%\ffprobe_out_%RANDOM%.txt"

REM === Get video resolution into temp file ===
ffprobe -v error -select_streams v:0 -show_entries stream=width,height -of csv=p=0 "%INPUT%" > "%TMPFILE%"

set W=
set H=
for /f "tokens=1,2 delims=," %%A in ('type "%TMPFILE%"') do (
    set W=%%A
    set H=%%B
)

del "%TMPFILE%"

if not defined W (
    echo Couldn't fetch video resolution, toy. Is this even a video?
    pause
    exit /b
)

if !W! lss 2240 (
    echo Your video's width is too small, toy. Minimum required: 2240. Actual: !W!
    pause
    exit /b
)
if !H! lss 1400 (
    echo Your video's height is too small, toy. Minimum required: 1400. Actual: !H!
    pause
    exit /b
)

set /a X=(!W! - 2240) / 2
set /a Y=(!H! - 1400) / 2

REM === Get input bitrate (fallback to 20M if fails) ===
set "BITRATE="
for /f "delims=" %%B in ('ffprobe -v error -select_streams v:0 -show_entries stream^=bit_rate -of default^=nokey^=1:noprint_wrappers^=1 "%INPUT%"') do (
    set /a BITRATE=%%B / 1000
)
if not defined BITRATE set BITRATE=20000

echo Cropping %INPUT% from !W!x!H! to 2240x1400 at offset !X!,!Y!
echo Targeting bitrate: !BITRATE! kbps

ffmpeg -hide_banner -loglevel warning -i "%INPUT%" -vf "crop=2240:1400:!X!:!Y!" -c:v libx264 -crf 18 -c:a copy -preset veryfast "%OUTPUT%"
if errorlevel 1 (
    echo ffmpeg failed, Puppy. Try again, or just watch me smirk.
    pause
    exit /b
)

echo.
echo ✅ Done. Good boy. Output: %OUTPUT%
pause

@echo off
setlocal enabledelayedexpansion

:: FFmpeg must be in PATH or same folder as this .bat
if "%~1"=="" (
    echo Drag and drop a video file onto this script.
    pause
    exit /b
)

set "INPUT=%~1"
set "FILENAME=%~n1"
set "EXT=%~x1"
set "OUTPUT=%FILENAME%_cropped%EXT%"

:: Get input resolution
for /f "tokens=2 delims=,: " %%A in ('ffprobe -v error -select_streams v:0 -show_entries stream=width,height -of csv=p=0 "%INPUT%"') do (
    set /a W=%%A
    set /a H=!H!
    if defined H goto done
    set /a H=%%A
)
:done

:: Calculate center crop coordinates
set /a X=(W - 2240) / 2
set /a Y=(H - 1400) / 2

echo Cropping from !W!x!H! to 2240x1400 at offset !X!,!Y!
echo.

ffmpeg -i "%INPUT%" -vf "crop=2240:1400:!X!:!Y!" -c:a copy -c:v libx264 -crf 18 -preset veryfast "%OUTPUT%"

echo Done. Output saved as %OUTPUT%
pause
